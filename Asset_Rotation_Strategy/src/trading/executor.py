"""
Trading executor for the Asset Rotation Strategy.
Handles the execution of trades based on the strategy signals.
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import time
import ccxt

from ..config_manager import get_exchange_credentials, get_trading_config
from .account import AccountManager
from .order import OrderManager
from .risk_manager import RiskManager
from .paper_trading import PaperTradingExecutor
from .trade_logger import trade_logger
from ..utils.precision import adjust_amount_for_precision, round_to_precision

# Exchange to default quote currency mapping
EXCHANGE_QUOTE_CURRENCIES = {
    'bitvavo': 'EUR',
    'kraken': 'EUR',
    'binance': 'USDC',
    'coinbase': 'USD',
    'bybit': 'USDT',
    'okx': 'USDT'
}

class TradingExecutor:
    """
    Executes trades based on the asset rotation strategy signals.
    """

    # Known non-existent market symbols on Binance
    KNOWN_NONEXISTENT_SYMBOLS = {
        'MANA/USDC',
        'SC/USDC',
        'GLMR/USDC',
        'LQTY/USDC'
    }

    def __init__(self, exchange_id: str = 'binance', test_mode: bool = False, config_path: str = None):
        """
        Initialize the trading executor.

        Args:
            exchange_id: The exchange ID (e.g., 'binance').
            test_mode: Whether running in test mode (skip authenticated calls).
            config_path: Path to the configuration file.
        """
        self.exchange_id = exchange_id.lower()
        self.trading_config = get_trading_config(config_path)
        self.credentials = get_exchange_credentials(exchange_id, config_path)
        self.test_mode = test_mode

        # Initialize components
        self.account_manager = AccountManager(exchange_id, test_mode=test_mode, config_path=config_path)
        self.order_manager = OrderManager(exchange_id, test_mode=test_mode, config_path=config_path)
        self.risk_manager = RiskManager(config_path=config_path)

        # Initialize paper trading executor if in paper mode
        if self.trading_config.get('mode') == 'paper':
            # Determine quote currency based on exchange mapping
            quote_currency = EXCHANGE_QUOTE_CURRENCIES.get(exchange_id.lower(), 'USDC')

            self.paper_trading = PaperTradingExecutor(
                initial_balance=self.trading_config.get('initial_capital', 10000.0),
                quote_currency=quote_currency
            )
            logging.info(f"Paper trading initialized with {quote_currency} as quote currency")
        else:
            self.paper_trading = None

        # State variables
        self.current_asset = None
        self.current_portfolio = {}  # Dictionary to track current portfolio {symbol: weight}
        self.last_trade_time = None
        self.is_trading_enabled = self.trading_config.get('enabled', False)

        logging.info(f"Trading executor initialized for {exchange_id}")
        logging.info(f"Trading mode: {self.trading_config.get('mode', 'paper')}")
        logging.info(f"Trading enabled: {self.is_trading_enabled}")

    def detect_quote_currency(self, assets_with_weights: Dict[str, float] = None) -> str:
        """
        Detect the quote currency using a flexible approach:
        1. First try to extract from trading assets
        2. Fallback to exchange mapping
        3. Final fallback to USDC

        Args:
            assets_with_weights: Dictionary of assets with their weights

        Returns:
            The detected quote currency (e.g., 'EUR', 'USDC', 'USD')
        """
        # Method 1: Extract from trading assets
        if assets_with_weights:
            first_asset = list(assets_with_weights.keys())[0]
            if '/' in first_asset:
                quote_currency = first_asset.split('/')[1]
                logging.debug(f"Quote currency detected from assets: {quote_currency}")
                return quote_currency

        # Method 2: Use exchange mapping
        exchange_quote = EXCHANGE_QUOTE_CURRENCIES.get(self.exchange_id.lower())
        if exchange_quote:
            logging.debug(f"Quote currency detected from exchange mapping: {exchange_quote}")
            return exchange_quote

        # Method 3: Final fallback
        logging.debug("Using default quote currency: USDC")
        return 'USDC'

    def _synchronize_time_with_server(self):
        """
        Synchronize time with server and return the time difference.

        Returns:
            Time difference in milliseconds that should be subtracted from local time
        """
        try:
            import requests

            # For Binance, get server time
            if self.exchange_id == 'binance':
                # Make multiple requests to get a more accurate measurement
                measurements = []
                for _ in range(3):
                    start_time = time.time() * 1000
                    response = requests.get('https://api.binance.com/api/v3/time', timeout=5)
                    end_time = time.time() * 1000
                    response.raise_for_status()

                    server_time = response.json()['serverTime']
                    # Account for network latency by using the midpoint
                    local_time = (start_time + end_time) / 2
                    offset = local_time - server_time
                    measurements.append(offset)

                # Use the median measurement to reduce noise
                offset = sorted(measurements)[1]

                logging.info(f"Time synchronization - Local ahead by: {offset:.0f}ms")

                # Return the offset that should be subtracted from local timestamps (as integer)
                return int(offset)
            else:
                # For other exchanges, return 0 (no offset)
                return 0

        except Exception as e:
            logging.warning(f"Failed to synchronize time with server: {e}. Using default offset of 0.")
            return 0

    def filter_nonexistent_positions(self, positions: Dict[str, Any]) -> Dict[str, Any]:
        """
        Filter out positions for known non-existent market symbols.

        Args:
            positions: Dictionary of symbol -> position data

        Returns:
            Filtered positions dictionary
        """
        filtered_positions = {
            symbol: position for symbol, position in positions.items()
            if symbol not in self.KNOWN_NONEXISTENT_SYMBOLS
        }

        # Log filtered out positions
        filtered_out = set(positions.keys()) - set(filtered_positions.keys())
        if filtered_out:
            logging.info(f"Filtered out {len(filtered_out)} positions for known non-existent market symbols: {', '.join(sorted(filtered_out))}")

        return filtered_positions

    def execute_strategy_signal(self, best_asset: str, mtpi_signal: int = 1) -> Dict[str, Any]:
        """
        Execute a trade based on the strategy signal for a single asset.

        Args:
            best_asset: The symbol of the best asset to buy (e.g., 'BTC/USDT').
            mtpi_signal: The MTPI signal (1 for bullish, -1 for bearish).

        Returns:
            A dictionary with the execution results.
        """
        trading_mode = self.trading_config.get('mode', 'paper')
        logging.info(f"Executing strategy signal: best_asset={best_asset}, mtpi_signal={mtpi_signal}, mode={trading_mode}")

        if not self.is_trading_enabled:
            logging.warning("Trading is disabled. No trades will be executed.")
            result = {'success': False, 'reason': 'Trading disabled'}
            trade_logger.log_trade(
                timestamp=datetime.now().isoformat(),
                action="none",
                symbol=best_asset if best_asset else "none",
                amount=0.0,
                price=0.0,
                order_id="none",
                trading_mode=trading_mode,
                success=False,
                reason='Trading disabled'
            )
            return result

        # Check if MTPI signal allows trading
        if mtpi_signal != 1:
            logging.info(f"MTPI signal is bearish ({mtpi_signal}). Exiting all positions.")
            exit_result = self.exit_all_positions()
            trade_logger.log_trade_result(exit_result, trading_mode)
            return exit_result

        # Check if we need to make a trade
        if best_asset == self.current_asset:
            logging.info(f"Already holding the best asset: {best_asset}. No trade needed.")
            result = {'success': True, 'reason': 'Already holding best asset', 'symbol': best_asset}
            trade_logger.log_trade(
                timestamp=datetime.now().isoformat(),
                action="hold",
                symbol=best_asset,
                amount=0.0,
                price=0.0,
                order_id="none",
                trading_mode=trading_mode,
                success=True,
                reason='Already holding best asset'
            )
            return result

        # Get current positions count for max position check
        if self.trading_config.get('mode') == 'paper':
            current_positions = self.paper_trading.get_positions()
        else:
            current_positions = self.account_manager.get_open_positions()

        # Check if we have reached the maximum number of open positions
        if not self.risk_manager.check_max_open_positions(len(current_positions)):
            logging.warning("Maximum number of open positions reached. No new positions will be opened.")
            result = {'success': False, 'reason': 'Maximum positions reached', 'symbol': best_asset}
            trade_logger.log_trade(
                timestamp=datetime.now().isoformat(),
                action="buy",
                symbol=best_asset,
                amount=0.0,
                price=0.0,
                order_id="none",
                trading_mode=trading_mode,
                success=False,
                reason='Maximum positions reached'
            )
            return result

        # Check if we have reached the daily trade limit
        if not self.risk_manager.check_daily_trade_limit(best_asset):
            logging.warning(f"Daily trade limit reached for {best_asset}. No new trades will be executed.")
            result = {'success': False, 'reason': 'Daily trade limit reached', 'symbol': best_asset}
            trade_logger.log_trade(
                timestamp=datetime.now().isoformat(),
                action="buy",
                symbol=best_asset,
                amount=0.0,
                price=0.0,
                order_id="none",
                trading_mode=trading_mode,
                success=False,
                reason='Daily trade limit reached'
            )
            return result

        # Store previous asset for notification
        previous_asset = self.current_asset
        exit_result = None

        # Exit current position if we have one
        if self.current_asset:
            logging.info(f"Exiting current position: {self.current_asset} before entering new position: {best_asset}")
            exit_result = self.exit_position(self.current_asset)
            trade_logger.log_trade_result(exit_result, trading_mode)

            if not exit_result.get('success', False):
                logging.error(f"Failed to exit position: {exit_result.get('reason')}")
                return exit_result

        # Enter new position
        logging.info(f"ASSET SELECTION - Attempting to enter position for selected asset: {best_asset}")
        entry_result = self.enter_position(best_asset)
        trade_logger.log_trade_result(entry_result, trading_mode)

        # Update current asset if successful
        if entry_result.get('success', False):
            self.current_asset = best_asset
            self.current_portfolio = {best_asset: 1.0}  # 100% allocation to the best asset
            self.last_trade_time = datetime.now()
            logging.info(f"TRADE SUCCESS - {best_asset}: Successfully updated current asset")

            # Send detailed asset rotation notification if we had a previous asset
            if previous_asset and hasattr(self, 'notification_manager') and self.notification_manager:
                self._send_asset_rotation_notification(previous_asset, exit_result, best_asset, entry_result)
        else:
            error_type = entry_result.get('error_type', 'unknown')
            error_reason = entry_result.get('reason', 'Unknown error')
            logging.error(f"TRADE FAILURE - {best_asset}: Failed to enter position")
            logging.error(f"TRADE FAILURE - {best_asset}: Error type: {error_type}")
            logging.error(f"TRADE FAILURE - {best_asset}: Error reason: {error_reason}")

            # Log this as a high-priority asset rejection
            logging.warning(f"HIGH-PRIORITY ASSET REJECTED: {best_asset} - {error_reason}")

        return entry_result

    def execute_multi_asset_strategy(self, assets_with_weights: Dict[str, float], mtpi_signal: int = 1, asset_scores: Dict[str, float] = None) -> Dict[str, Any]:
        """
        Execute a trade based on the strategy signal for multiple assets with weights.

        Args:
            assets_with_weights: Dictionary mapping asset symbols to their allocation weights.
                                Example: {'BTC/USDT': 0.7, 'ETH/USDT': 0.2, 'SOL/USDT': 0.1}
            mtpi_signal: The MTPI signal (1 for bullish, -1 for bearish).
            asset_scores: Optional dictionary of asset scores for replacement logic.

        Returns:
            A dictionary with the execution results.
        """
        if not self.is_trading_enabled:
            logging.warning("Trading is disabled. No trades will be executed.")
            return {'success': False, 'reason': 'Trading disabled'}

        # Check if MTPI signal allows trading
        if mtpi_signal != 1:
            logging.info(f"MTPI signal is bearish ({mtpi_signal}). Exiting all positions.")
            return self.exit_all_positions()

        # Validate input
        if not assets_with_weights:
            logging.error("No assets provided for multi-asset strategy.")
            return {'success': False, 'reason': 'No assets provided'}

        # Log the original assets and weights for debugging
        logging.info(f"Original assets with weights: {assets_with_weights}")

        # Check if the weights sum to approximately 1.0
        total_weight = sum(assets_with_weights.values())
        if abs(total_weight - 1.0) > 0.001:
            logging.warning(f"Asset weights do not sum to 1.0 (sum: {total_weight}). Normalizing weights.")
            # Normalize weights
            assets_with_weights = {asset: weight / total_weight for asset, weight in assets_with_weights.items()}

        # Check if we need to make a trade by comparing with current portfolio
        if self.current_portfolio == assets_with_weights:
            logging.info("Already holding the same portfolio with the same weights. No trade needed.")
            return {'success': True, 'reason': 'Already holding same portfolio'}

        # Check if we have reached the maximum number of open positions
        max_positions = self.trading_config.get('risk_management', {}).get('max_open_positions', 5)
        if len(assets_with_weights) > max_positions:
            logging.warning(f"Strategy requires {len(assets_with_weights)} positions, but maximum allowed is {max_positions}.")
            # Truncate to the top N assets by weight
            sorted_assets = sorted(assets_with_weights.items(), key=lambda x: x[1], reverse=True)
            assets_with_weights = dict(sorted_assets[:max_positions])
            # Renormalize weights
            total_weight = sum(assets_with_weights.values())
            assets_with_weights = {asset: weight / total_weight for asset, weight in assets_with_weights.items()}
            logging.info(f"Truncated to top {max_positions} assets and renormalized weights.")

        # Identify assets to sell and buy
        current_assets = set(self.current_portfolio.keys())
        new_assets = set(assets_with_weights.keys())

        assets_to_sell = current_assets - new_assets
        assets_to_keep = current_assets.intersection(new_assets)
        assets_to_buy = new_assets - current_assets

        # Track asset swaps: map sold assets to their replacement assets
        # This helps us use the full proceeds from sold assets for their replacements
        asset_swaps = {}  # {sold_asset: replacement_asset}
        swap_proceeds = {}  # {replacement_asset: proceeds_from_sold_asset}

        # Check daily trade limits for new assets
        # Use the provided asset_scores if available, otherwise create an empty dict
        if asset_scores is None:
            asset_scores = {}
            logging.warning("No asset scores provided to execute_multi_asset_strategy. Replacement logic may not work properly.")
        else:
            logging.info(f"Using provided asset scores for replacement logic: {asset_scores}")
            # Store asset scores as an instance attribute for debugging
            self.asset_scores = asset_scores

        # Log the top assets by score for diagnostic purposes
        if asset_scores:
            top_assets = sorted(asset_scores.items(), key=lambda x: x[1], reverse=True)[:10]
            logging.info("Top assets by score:")
            for asset, score in top_assets:
                # Check if this asset would hit daily trade limit
                would_hit_limit = not self.risk_manager.check_daily_trade_limit(asset, increment=False)
                limit_status = "WOULD HIT LIMIT" if would_hit_limit else "OK"
                logging.info(f"  {asset}: score={score}, daily trade limit: {limit_status}")

        # Create a copy of assets_to_buy to avoid modifying it during iteration
        assets_to_buy_list = list(assets_to_buy)

        # Dictionary to track rejected assets and reasons
        rejected_assets = {}

        for asset in assets_to_buy_list:
            # Check if this asset would hit daily trade limit
            if not self.risk_manager.check_daily_trade_limit(asset):
                logging.warning(f"Daily trade limit reached for {asset}. Removing from buy list.")
                assets_to_buy.remove(asset)

                # Store rejection reason
                rejected_assets[asset] = "Daily trade limit reached"

                # Store original weight for possible replacement
                original_weight = assets_with_weights[asset]
                del assets_with_weights[asset]

                # Try to find a replacement using the provided asset scores
                if asset_scores:
                    # Find all assets not currently in the buy list but with good scores
                    potential_replacements = {}
                    for a, score in asset_scores.items():
                        # Check daily trade limit without incrementing the counter
                        if a not in assets_to_buy and a not in assets_to_keep and self.risk_manager.check_daily_trade_limit(a, increment=False):
                            potential_replacements[a] = score

                    if potential_replacements:
                        # Get the highest scoring replacement
                        replacement = max(potential_replacements.items(), key=lambda x: x[1])[0]
                        logging.info(f"Replacing {asset} with next highest scoring asset: {replacement} (score: {potential_replacements[replacement]})")
                        assets_to_buy.add(replacement)
                        assets_with_weights[replacement] = original_weight
                        continue
                    else:
                        logging.warning(f"No suitable replacement found for {asset}. No assets available that meet trade limit requirements.")

                # If no replacement found, adjust weights for remaining assets
                if assets_to_buy or assets_to_keep:
                    remaining_weight = sum(assets_with_weights[a] for a in assets_to_buy.union(assets_to_keep))
                    if remaining_weight > 0:
                        for a in assets_to_buy.union(assets_to_keep):
                            assets_with_weights[a] = assets_with_weights[a] / remaining_weight

        # Execute trades
        results = {
            'success': True,
            'trades': [],
            'errors': [],
            'rejected_assets': rejected_assets  # Add rejected assets to results
        }

        # Track all trades for potential rollback and failure recovery
        executed_trades = []
        critical_failure = False
        total_trades_attempted = len(assets_to_sell) + len(assets_to_keep) + len(assets_to_buy)

        # Log rejected assets
        if rejected_assets:
            logging.info("Assets rejected during execution:")
            for asset, reason in rejected_assets.items():
                logging.info(f"  {asset}: {reason}")
        else:
            logging.info("No assets were rejected during execution")

        # Step 1: Exit positions for assets we're no longer holding
        for asset in assets_to_sell:
            exit_result = self.exit_position(asset)
            results['trades'].append(exit_result)

            if not exit_result.get('success', False):
                logging.error(f"Failed to exit position for {asset}: {exit_result.get('reason')}")
                results['errors'].append({
                    'asset': asset,
                    'action': 'sell',
                    'reason': exit_result.get('reason', 'Unknown error')
                })
                results['success'] = False
            else:
                # Track proceeds from successful sales for proportional distribution
                proceeds = exit_result.get('proceeds', 0.0)
                if proceeds > 0:
                    original_weight = self.current_portfolio.get(asset, 0.0)
                    logging.info(f"Asset {asset} sold for {proceeds:.8f} {quote_currency} (original weight: {original_weight:.4f})")

                    # Store proceeds for proportional distribution to new assets
                    # No longer assume 1:1 mapping - will distribute proportionally later
                    asset_swaps[asset] = proceeds
                    logging.info(f"Stored {proceeds:.8f} {quote_currency} proceeds from {asset} for proportional distribution")

        # Get available balance and calculate total portfolio value for consistent calculations
        quote_currency = self.detect_quote_currency(assets_with_weights)

        if self.trading_config.get('mode') == 'paper':
            available_balance = self.paper_trading.get_balance().get(quote_currency, 0.0)
            current_positions = self.paper_trading.get_positions()
        else:
            available_balance = self.account_manager.get_balance(quote_currency)
            current_positions = self.account_manager.get_open_positions()

        # Calculate total portfolio value once and cache position data
        total_portfolio_value = available_balance
        cached_position_data = {}

        for asset, position in current_positions.items():
            price = self.get_current_price(asset)
            if price:
                position_size = position.get('amount', 0.0)
                position_value = position_size * price
                cached_position_data[asset] = {
                    'size': position_size,
                    'price': price,
                    'value': position_value
                }
                total_portfolio_value += position_value
            else:
                logging.warning(f"Could not get price for {asset} during portfolio value calculation")

        logging.info(f"Total portfolio value (cached): {total_portfolio_value:.8f} {quote_currency}")

        # Pre-check if we have enough balance for all planned buys
        total_required_balance = 0
        assets_to_buy_with_prices = []

        # Get transaction fee rate from config or use default
        fee_rate = self.trading_config.get('transaction_fee_rate', 0.001)  # Default 0.1%

        # Apply position size percentage from config
        position_size_pct = self.trading_config.get('position_size_pct', 100.0)
        safe_available_balance = round_to_precision(available_balance * (position_size_pct / 100.0), 0.00000001)
        logging.info(f"Using safe available balance: {safe_available_balance:.8f} ({position_size_pct}% of {available_balance:.8f})")

        # First get prices for all assets we plan to buy
        for asset in assets_to_buy:
            weight = assets_with_weights.get(asset, 0.0)
            price = self.get_current_price(asset)
            if price:
                # Calculate required balance with precision rounding
                required_balance = round_to_precision(safe_available_balance * weight, 0.00000001)

                # Calculate the base amount (quantity) with precision requirements
                base_amount = required_balance / price
                adjusted_base_amount = adjust_amount_for_precision(
                    base_amount,
                    asset,
                    price=price,
                    is_buy=True,
                    trading_config=self.trading_config
                )

                # Recalculate the actual required balance after precision adjustment
                adjusted_required_balance = adjusted_base_amount * price

                # Add fee to required balance
                adjusted_required_balance_with_fee = adjusted_required_balance * (1 + fee_rate)

                # Round to 8 decimal places
                adjusted_required_balance_with_fee = round_to_precision(adjusted_required_balance_with_fee, 0.00000001)

                total_required_balance += adjusted_required_balance_with_fee
                assets_to_buy_with_prices.append((asset, weight, price, adjusted_required_balance_with_fee, adjusted_base_amount))

                logging.info(f"Asset {asset}: weight={weight:.4f}, price={price:.8f}, required={adjusted_required_balance_with_fee:.8f}, amount={adjusted_base_amount:.8f}")
            else:
                logging.error(f"Failed to get price for {asset}, cannot calculate required balance")
                results['errors'].append({
                    'asset': asset,
                    'action': 'buy',
                    'reason': 'Failed to get price'
                })
                results['success'] = False

        # Check if we have enough balance for all planned buys
        if total_required_balance > safe_available_balance:
            logging.warning(f"Insufficient balance for all planned buys. Required: {total_required_balance:.8f}, Available: {safe_available_balance:.8f}")

            # Sort assets by weight first, then by asset name to ensure consistent ordering
            # This ensures that when weights are equal (as in equal allocation),
            # assets are processed in a consistent order based on their name
            # Note: For equal allocation, this will preserve the score-based ordering from background_service.py
            assets_to_buy_with_prices.sort(key=lambda x: (x[1], x[0]), reverse=True)

            # Log the sorted assets for debugging
            logging.info(f"Sorted assets for purchase (by weight, then name): {[asset[0] for asset in assets_to_buy_with_prices]}")

            # Calculate how many assets we can actually buy
            cumulative_balance = 0
            affordable_assets = []

            for asset, weight, price, required_balance, base_amount in assets_to_buy_with_prices:
                # Add a small buffer (0.1%) to ensure we don't exceed available balance
                if cumulative_balance + required_balance <= safe_available_balance * 0.999:
                    affordable_assets.append(asset)
                    cumulative_balance += required_balance
                else:
                    logging.warning(f"Cannot afford to buy {asset} with weight {weight:.1%}, requires {required_balance:.8f}")
                    results['errors'].append({
                        'asset': asset,
                        'action': 'buy',
                        'reason': 'Insufficient balance'
                    })

            # Update assets_to_buy to only include affordable assets
            assets_to_buy = set(affordable_assets)

            if not assets_to_buy:
                logging.error("Cannot afford to buy any assets with current balance")
                results['success'] = False
                return results

            # Recalculate weights for the affordable assets
            total_weight = sum(assets_with_weights[asset] for asset in assets_to_buy)
            for asset in assets_to_buy:
                assets_with_weights[asset] = assets_with_weights[asset] / total_weight

            logging.info(f"Adjusted weights for affordable assets: {assets_with_weights}")

        # Log the final list of assets to buy
        logging.info(f"Final assets to buy: {assets_to_buy}")

        # Step 2: Adjust positions for assets we're keeping but with different weights
        # CRITICAL FIX: Process SELL orders first, then BUY orders to ensure liquidity
        # This fixes the systematic issue where buy orders would fail due to insufficient balance
        # when the system tried to increase positions before reducing others

        # First, collect all position adjustments needed
        position_adjustments = []

        for asset in assets_to_keep:
            current_weight = self.current_portfolio.get(asset, 0.0)
            new_weight = assets_with_weights.get(asset, 0.0)

            # Skip if weights are very close (within 1%)
            if abs(current_weight - new_weight) < 0.01:
                logging.info(f"Weight for {asset} unchanged (current: {current_weight:.2f}, new: {new_weight:.2f}). Skipping adjustment.")
                continue

            # Use cached position data for consistent calculations
            if asset not in cached_position_data:
                logging.error(f"No cached position data for {asset}")
                results['errors'].append({
                    'asset': asset,
                    'action': 'adjust',
                    'reason': 'No cached position data'
                })
                results['success'] = False
                continue

            position_data = cached_position_data[asset]
            position_size = position_data['size']
            price = position_data['price']
            position_value = position_data['value']

            # Calculate target position value using cached total portfolio value
            target_position_value = total_portfolio_value * new_weight

            # Store adjustment info for later processing
            if target_position_value != position_value:
                position_adjustments.append({
                    'asset': asset,
                    'current_weight': current_weight,
                    'new_weight': new_weight,
                    'position_size': position_size,
                    'position_value': position_value,
                    'target_position_value': target_position_value,
                    'price': price,
                    'action': 'buy' if target_position_value > position_value else 'sell'
                })

        # Step 2a: Process all SELL orders first to free up capital
        logging.info("Processing position reductions (sells) first to free up capital...")
        for adjustment in position_adjustments:
            if adjustment['action'] == 'sell':
                asset = adjustment['asset']
                position_value = adjustment['position_value']
                target_position_value = adjustment['target_position_value']
                price = adjustment['price']

                # Need to sell some
                reduce_value = position_value - target_position_value
                reduce_size = reduce_value / price

                # Check if reduce_size meets minimum order requirements
                adjusted_reduce_size = adjust_amount_for_precision(
                    reduce_size,
                    asset,
                    price=price,
                    is_buy=False,  # This is a sell order
                    trading_config=self.trading_config
                )

                if adjusted_reduce_size <= 0:
                    logging.warning(f"Position reduction size {reduce_size:.8f} for {asset} is below minimum order requirements. Skipping reduction.")
                    results['trades'].append({
                        'success': True,
                        'symbol': asset,
                        'side': 'sell',
                        'amount': 0,
                        'price': price,
                        'order': None,
                        'skipped': True,
                        'reason': 'Reduction below minimum order size - skipped'
                    })
                    continue

                # Execute sell order
                if self.trading_config.get('mode') == 'paper':
                    order = self.paper_trading.create_market_sell_order(
                        asset, reduce_size, price
                    )
                else:
                    order = self.order_manager.create_market_sell_order(
                        asset, reduce_size
                    )

                if order:
                    logging.info(f"Reduced position for {asset}: -{reduce_size:.6f} units (${reduce_value:.2f})")
                    results['trades'].append({
                        'success': True,
                        'symbol': asset,
                        'side': 'sell',
                        'amount': reduce_size,
                        'price': price,
                        'order': order
                    })
                else:
                    logging.error(f"Failed to reduce position for {asset}")
                    results['errors'].append({
                        'asset': asset,
                        'action': 'sell',
                        'reason': 'Failed to create order'
                    })
                    results['success'] = False

        # Update available balance after sells
        if self.trading_config.get('mode') == 'paper':
            available_balance = self.paper_trading.get_balance().get(quote_currency, 0.0)
        else:
            available_balance = self.account_manager.get_balance(quote_currency)

        logging.info(f"Available balance after position reductions: {available_balance:.8f} {quote_currency}")

        # Step 2b: Process all BUY orders after capital has been freed up
        logging.info("Processing position increases (buys) after capital has been freed...")
        for adjustment in position_adjustments:
            if adjustment['action'] == 'buy':
                asset = adjustment['asset']
                position_value = adjustment['position_value']
                target_position_value = adjustment['target_position_value']
                price = adjustment['price']

                # Need to buy more
                additional_value = target_position_value - position_value
                additional_size = additional_value / price

                # Execute buy order
                if self.trading_config.get('mode') == 'paper':
                    order = self.paper_trading.create_market_buy_order(
                        asset, additional_value, price
                    )
                else:
                    order = self.order_manager.create_market_buy_order(
                        asset, additional_value
                    )

                if order:
                    logging.info(f"Increased position for {asset}: +{additional_size:.6f} units (${additional_value:.2f})")
                    results['trades'].append({
                        'success': True,
                        'symbol': asset,
                        'side': 'buy',
                        'amount': additional_size,
                        'price': price,
                        'order': order
                    })
                else:
                    logging.error(f"Failed to increase position for {asset}")
                    results['errors'].append({
                        'asset': asset,
                        'action': 'buy',
                        'reason': 'Failed to create order'
                    })
                    results['success'] = False

        # Available balance is already updated after Step 2b

        # Step 3: Enter positions for new assets
        # Calculate proportional distribution of sale proceeds to new assets
        total_sale_proceeds = sum(asset_swaps.values()) if asset_swaps else 0.0
        total_new_asset_weight = sum(assets_with_weights.get(asset, 0.0) for asset in assets_to_buy)

        logging.info(f"Total sale proceeds to distribute: {total_sale_proceeds:.8f} {quote_currency}")
        logging.info(f"Total weight of new assets: {total_new_asset_weight:.4f}")

        for asset in assets_to_buy:
            weight = assets_with_weights.get(asset, 0.0)

            # Get current price (we already checked this in pre-check, but get it again in case it changed)
            price = self.get_current_price(asset)
            if not price:
                logging.error(f"Failed to get price for {asset}")
                results['errors'].append({
                    'asset': asset,
                    'action': 'buy',
                    'reason': 'Failed to get price'
                })
                results['success'] = False
                continue

            # Calculate position value using proportional distribution of sale proceeds
            if total_sale_proceeds > 0 and total_new_asset_weight > 0:
                # Distribute sale proceeds proportionally based on target weights
                proportional_proceeds = (weight / total_new_asset_weight) * total_sale_proceeds
                # Add any additional allocation from safe available balance
                additional_from_balance = safe_available_balance * weight
                position_value = round_to_precision(proportional_proceeds + additional_from_balance, 0.00000001)
                logging.info(f"Position value for {asset}: {position_value:.8f} {quote_currency} (proceeds: {proportional_proceeds:.8f} + balance: {additional_from_balance:.8f})")
            else:
                # No sale proceeds - use only safe available balance
                position_value = round_to_precision(safe_available_balance * weight, 0.00000001)
                logging.info(f"Using weight-based allocation for {asset}: {position_value:.8f} {quote_currency} ({weight:.1%} of {safe_available_balance:.8f})")

            # Calculate base amount with precision requirements
            base_amount = position_value / price
            position_size = adjust_amount_for_precision(
                base_amount,
                asset,
                price=price,
                is_buy=True,
                trading_config=self.trading_config
            )

            # Recalculate the actual position value after precision adjustment
            adjusted_position_value = position_size * price

            # Execute buy order
            if position_size > 0:
                logging.info(f"Buying {asset}: {position_size:.8f} units at {price:.8f} (value: {adjusted_position_value:.8f})")
                if self.trading_config.get('mode') == 'paper':
                    order = self.paper_trading.create_market_buy_order(
                        asset, adjusted_position_value, price
                    )
                else:
                    order = self.order_manager.create_market_buy_order(
                        asset, adjusted_position_value
                    )

                if order:
                    logging.info(f"Entered position for {asset}: {position_size:.6f} units (${position_value:.2f})")
                    results['trades'].append({
                        'success': True,
                        'symbol': asset,
                        'side': 'buy',
                        'amount': position_size,
                        'price': price,
                        'order': order
                    })

                    # Update portfolio state
                    self.current_portfolio[asset] = weight

                    # If this was a swap, subtract the used proceeds from available balance
                    # to avoid double-counting in subsequent calculations
                    if asset in swap_proceeds:
                        available_balance -= swap_proceeds[asset]
                        logging.info(f"Reduced available balance by swap proceeds: {swap_proceeds[asset]:.8f} {quote_currency}")
                else:
                    logging.error(f"Failed to enter position for {asset}")
                    results['errors'].append({
                        'asset': asset,
                        'action': 'buy',
                        'reason': 'Failed to create order'
                    })
                    results['success'] = False

        # Update portfolio state if any trades were successful
        successful_trades = [t for t in results['trades'] if t.get('success', False)]
        if successful_trades:
            # Create a new portfolio based on successful trades only
            updated_portfolio = {}

            # Keep existing positions that weren't sold
            for asset in self.current_portfolio:
                if asset not in assets_to_sell:
                    updated_portfolio[asset] = self.current_portfolio[asset]

            # Add successfully bought assets
            for trade in successful_trades:
                if trade.get('side') == 'buy':
                    asset = trade.get('symbol')
                    if asset in assets_with_weights:
                        updated_portfolio[asset] = assets_with_weights[asset]

            # Normalize weights if needed
            if updated_portfolio:
                total_weight = sum(updated_portfolio.values())
                if abs(total_weight - 1.0) > 0.001:
                    updated_portfolio = {asset: weight / total_weight for asset, weight in updated_portfolio.items()}

                logging.info(f"Updated portfolio with successful trades: {updated_portfolio}")
                self.current_portfolio = updated_portfolio
                self.last_trade_time = datetime.now()

                # For backward compatibility, set current_asset to the highest weight asset
                if updated_portfolio:
                    self.current_asset = max(updated_portfolio.items(), key=lambda x: x[1])[0]
                else:
                    self.current_asset = None
            else:
                logging.warning("No successful trades to update portfolio with")
        elif results['success']:
            # If overall success but no individual trade successes (unlikely)
            self.current_portfolio = assets_with_weights.copy()
            self.last_trade_time = datetime.now()

            # For backward compatibility, set current_asset to the highest weight asset
            if assets_with_weights:
                self.current_asset = max(assets_with_weights.items(), key=lambda x: x[1])[0]
            else:
                self.current_asset = None

        # Add execution analysis to results for monitoring and debugging
        successful_count = len([t for t in results['trades'] if t.get('success', False)])
        failed_count = len(results['errors'])
        success_rate = successful_count / total_trades_attempted if total_trades_attempted > 0 else 0

        results['execution_analysis'] = {
            'total_attempted': total_trades_attempted,
            'successful': successful_count,
            'failed': failed_count,
            'success_rate': success_rate,
            'critical_failure': success_rate < 0.5 and failed_count > 0
        }

        if results['execution_analysis']['critical_failure']:
            logging.warning(f"Critical portfolio rebalancing failure detected: {success_rate:.1%} success rate")
            results['success'] = False

        return results

    def enter_position(self, symbol: str) -> Dict[str, Any]:
        """
        Enter a position for the specified symbol.

        Args:
            symbol: The trading pair symbol (e.g., 'BTC/USDT').

        Returns:
            A dictionary with the execution results.
        """
        trading_mode = self.trading_config.get('mode', 'paper')
        logging.info(f"Attempting to enter position for {symbol} in {trading_mode} mode")

        try:
            # DEBUG: Enhanced logging for trade attempts
            logging.info(f"[DEBUG] TRADE - {symbol}: Starting enter_position attempt")
            logging.info(f"[DEBUG] TRADE - {symbol}: Trading mode: {trading_mode}")

            # Get current market price
            logging.info(f"TRADE ATTEMPT - {symbol}: Getting current market price...")
            price = self.get_current_price(symbol)

            # DEBUG: Detailed price fetch logging
            logging.info(f"[DEBUG] TRADE - {symbol}: get_current_price returned: {price}")
            logging.info(f"[DEBUG] TRADE - {symbol}: Price type: {type(price)}")
            logging.info(f"[DEBUG] TRADE - {symbol}: Price evaluation - not price: {not price}")
            logging.info(f"[DEBUG] TRADE - {symbol}: Price evaluation - price <= 0: {price <= 0 if price is not None else 'N/A'}")

            if not price:
                error_msg = f"Failed to get price for {symbol} (returned: {price})"
                logging.error(f"TRADE FAILURE - {symbol}: {error_msg}")
                logging.warning(f"[DEBUG] TRADE - {symbol}: Price fetch failed - this is why the asset is being rejected!")
                trade_logger.log_trade(
                    timestamp=datetime.now().isoformat(),
                    action="buy",
                    symbol=symbol,
                    amount=0.0,
                    price=0.0,
                    order_id="none",
                    trading_mode=trading_mode,
                    success=False,
                    reason=error_msg
                )
                return {'success': False, 'reason': error_msg, 'symbol': symbol, 'error_type': 'price_fetch_error'}

            logging.info(f"TRADE ATTEMPT - {symbol}: Current price: {price:.8f}")

            # Get available balance
            quote_currency = self.get_quote_currency(symbol)
            if trading_mode == 'paper':
                balance = self.paper_trading.get_balance(quote_currency)
                available_balance = balance.get(quote_currency, 0.0)
            else:
                available_balance = self.account_manager.get_balance(quote_currency)

            logging.info(f"Available balance for {quote_currency}: {available_balance:.8f}")

            if available_balance <= 0:
                error_msg = f"Insufficient balance: {available_balance} {quote_currency}"
                logging.error(f"TRADE FAILURE - {symbol}: {error_msg}")
                trade_logger.log_trade(
                    timestamp=datetime.now().isoformat(),
                    action="buy",
                    symbol=symbol,
                    amount=0.0,
                    price=price,
                    order_id="none",
                    trading_mode=trading_mode,
                    success=False,
                    reason=error_msg
                )
                return {'success': False, 'reason': error_msg, 'symbol': symbol, 'error_type': 'insufficient_balance'}

            # Calculate position size
            position_size = self.risk_manager.calculate_position_size(
                available_balance, symbol, price
            )

            logging.info(f"Calculated position size: {position_size:.8f} {symbol.split('/')[0]}")

            if position_size <= 0:
                error_msg = f"Invalid position size: {position_size}"
                logging.error(f"TRADE FAILURE - {symbol}: {error_msg}")
                trade_logger.log_trade(
                    timestamp=datetime.now().isoformat(),
                    action="buy",
                    symbol=symbol,
                    amount=0.0,
                    price=price,
                    order_id="none",
                    trading_mode=trading_mode,
                    success=False,
                    reason=error_msg
                )
                return {'success': False, 'reason': error_msg, 'symbol': symbol, 'error_type': 'invalid_position_size'}

            # Calculate the position value with precision
            position_value = round_to_precision(position_size * price, 0.00000001)

            # Log the exact values we're using
            logging.info(f"Entering position for {symbol}: {position_size:.8f} units at {price:.8f} (value: {position_value:.8f} {quote_currency})")

            # Execute the trade
            if trading_mode == 'paper':
                # Paper trading
                logging.info(f"Executing paper market buy order for {symbol}")
                order = self.paper_trading.create_market_buy_order(
                    symbol, position_value, price
                )
            else:
                # Live trading
                logging.info(f"Executing live market buy order for {symbol}")
                order = self.order_manager.create_market_buy_order(
                    symbol, position_value
                )

            if not order:
                error_msg = f"Failed to create order for {symbol}"
                logging.error(f"TRADE FAILURE - {symbol}: {error_msg}")
                trade_logger.log_trade(
                    timestamp=datetime.now().isoformat(),
                    action="buy",
                    symbol=symbol,
                    amount=position_size,
                    price=price,
                    order_id="none",
                    trading_mode=trading_mode,
                    success=False,
                    reason=error_msg
                )
                return {'success': False, 'reason': error_msg, 'symbol': symbol, 'error_type': 'order_creation_failed'}

            # Get the actual execution price (average price) if available
            execution_price = price  # Default to the pre-order price
            if 'average_price' in order and order['average_price'] is not None:
                execution_price = order['average_price']
            elif self.trading_config.get('mode') != 'paper' and 'average' in order and order['average'] is not None:
                execution_price = float(order['average'])
            elif 'price' in order and order['price'] is not None:
                execution_price = float(order['price'])

            # Log the filled amount and fee information
            filled_amount = order.get('filled', position_size)
            logging.info(f"Filled amount: {filled_amount:.8f} {symbol.split('/')[0]}")

            fee_info = {}
            if 'fee' in order and order['fee'] is not None:
                fee_cost = order['fee'].get('cost', 0)
                fee_currency = order['fee'].get('currency', '')
                fee_info = {
                    'cost': fee_cost,
                    'currency': fee_currency,
                    'rate': order['fee'].get('rate', 0.001)
                }
                logging.info(f"Order fee: {fee_cost:.8f} {fee_currency}")

            logging.info(f"Successfully entered position: {symbol}, amount: {filled_amount:.8f}, price: {execution_price:.8f}")

            # Create detailed result
            result = {
                'success': True,
                'symbol': symbol,
                'side': 'buy',
                'amount': position_size,
                'price': execution_price,  # Use the actual execution price
                'order': order,
                'filled_amount': filled_amount,
                'fee': fee_info,
                'quote_currency': quote_currency,
                'base_currency': symbol.split('/')[0],
                'timestamp': datetime.now().isoformat()
            }

            return result

        except Exception as e:
            error_msg = f"Error entering position for {symbol}: {e}"
            logging.error(f"TRADE FAILURE - {symbol}: {error_msg}", exc_info=True)
            trade_logger.log_trade(
                timestamp=datetime.now().isoformat(),
                action="buy",
                symbol=symbol,
                amount=0.0,
                price=price if 'price' in locals() else 0.0,
                order_id="none",
                trading_mode=trading_mode,
                success=False,
                reason=error_msg
            )
            return {'success': False, 'reason': str(e), 'symbol': symbol, 'error_type': 'exception'}

    def exit_position(self, symbol: str) -> Dict[str, Any]:
        """
        Exit a position for the specified symbol.

        Args:
            symbol: The trading pair symbol (e.g., 'BTC/USDT').

        Returns:
            A dictionary with the execution results.
        """
        trading_mode = self.trading_config.get('mode', 'paper')
        logging.info(f"Attempting to exit position for {symbol} in {trading_mode} mode")

        try:
            # Get current market price
            price = self.get_current_price(symbol)
            if price == -1.0:
                # Non-existent market symbol - skip this position
                warning_msg = f"Market symbol {symbol} does not exist on exchange. Skipping position exit."
                logging.warning(warning_msg)
                trade_logger.log_trade(
                    timestamp=datetime.now().isoformat(),
                    action="sell",
                    symbol=symbol,
                    amount=0.0,
                    price=0.0,
                    order_id="none",
                    trading_mode=trading_mode,
                    success=True,  # Mark as success since we're intentionally skipping
                    reason="Market symbol does not exist on exchange - skipped"
                )
                return {
                    'success': True,
                    'reason': 'Market symbol does not exist on exchange - skipped',
                    'symbol': symbol,
                    'skipped': True,
                    'nonexistent_symbol': True
                }
            elif not price or price <= 0:
                # Other price fetch errors
                error_msg = f"Failed to get price for {symbol}"
                logging.error(error_msg)
                trade_logger.log_trade(
                    timestamp=datetime.now().isoformat(),
                    action="sell",
                    symbol=symbol,
                    amount=0.0,
                    price=0.0,
                    order_id="none",
                    trading_mode=trading_mode,
                    success=False,
                    reason=error_msg
                )
                return {'success': False, 'reason': error_msg, 'symbol': symbol}

            # Get position size
            if trading_mode == 'paper':
                positions = self.paper_trading.get_positions()
                position = positions.get(symbol, {})
                position_size = position.get('amount', 0.0)
                entry_price = position.get('entry_price', 0.0)
            else:
                positions = self.account_manager.get_open_positions()
                position = positions.get(symbol, {})
                position_size = position.get('amount', 0.0)
                entry_price = position.get('entry_price', 0.0)

            logging.info(f"Current position for {symbol}: {position_size:.8f} units (entry price: {entry_price:.8f})")

            if position_size <= 0:
                error_msg = f"No position found for {symbol}"
                logging.error(error_msg)
                trade_logger.log_trade(
                    timestamp=datetime.now().isoformat(),
                    action="sell",
                    symbol=symbol,
                    amount=0.0,
                    price=price,
                    order_id="none",
                    trading_mode=trading_mode,
                    success=False,
                    reason=error_msg
                )
                return {'success': False, 'reason': error_msg, 'symbol': symbol}

            # Check if position size meets minimum order requirements
            adjusted_amount = adjust_amount_for_precision(
                position_size,
                symbol,
                price=price,
                is_buy=False,  # This is a sell order
                trading_config=self.trading_config
            )

            if adjusted_amount <= 0:
                warning_msg = f"Position size {position_size:.8f} for {symbol} is below minimum order requirements. Skipping sell order."
                logging.warning(warning_msg)
                trade_logger.log_trade(
                    timestamp=datetime.now().isoformat(),
                    action="sell",
                    symbol=symbol,
                    amount=position_size,
                    price=price,
                    order_id="none",
                    trading_mode=trading_mode,
                    success=True,  # Mark as success since we're intentionally skipping
                    reason="Position below minimum order size - skipped"
                )
                return {
                    'success': True,
                    'reason': 'Position below minimum order size - skipped',
                    'symbol': symbol,
                    'skipped': True,
                    'position_size': position_size,
                    'minimum_check_failed': True
                }

            # Calculate potential profit/loss
            pnl = (price - entry_price) * position_size
            pnl_pct = ((price / entry_price) - 1) * 100 if entry_price > 0 else 0
            logging.info(f"Potential P&L: {pnl:.8f} ({pnl_pct:.2f}%)")

            # Execute the trade
            if trading_mode == 'paper':
                # Paper trading
                logging.info(f"Executing paper market sell order for {symbol}")
                order = self.paper_trading.create_market_sell_order(
                    symbol, position_size, price
                )
            else:
                # Live trading
                logging.info(f"Executing live market sell order for {symbol}")
                order = self.order_manager.create_market_sell_order(
                    symbol, position_size
                )

            if not order:
                error_msg = f"Failed to create order for {symbol}"
                logging.error(error_msg)
                trade_logger.log_trade(
                    timestamp=datetime.now().isoformat(),
                    action="sell",
                    symbol=symbol,
                    amount=position_size,
                    price=price,
                    order_id="none",
                    trading_mode=trading_mode,
                    success=False,
                    reason=error_msg
                )
                return {'success': False, 'reason': error_msg, 'symbol': symbol}

            # Get the actual execution price (average price) if available
            execution_price = price  # Default to the pre-order price
            if 'average_price' in order and order['average_price'] is not None:
                execution_price = order['average_price']
            elif self.trading_config.get('mode') != 'paper' and 'average' in order and order['average'] is not None:
                execution_price = float(order['average'])
            elif 'price' in order and order['price'] is not None:
                execution_price = float(order['price'])

            # Log the filled amount and fee information
            filled_amount = order.get('filled', position_size)
            logging.info(f"Filled amount: {filled_amount:.8f} {symbol.split('/')[0]}")

            fee_info = {}
            if 'fee' in order and order['fee'] is not None:
                fee_cost = order['fee'].get('cost', 0)
                fee_currency = order['fee'].get('currency', '')
                fee_info = {
                    'cost': fee_cost,
                    'currency': fee_currency,
                    'rate': order['fee'].get('rate', 0.001)
                }
                logging.info(f"Order fee: {fee_cost:.8f} {fee_currency}")

            # Calculate actual profit/loss
            actual_pnl = (execution_price - entry_price) * filled_amount
            actual_pnl_pct = ((execution_price / entry_price) - 1) * 100 if entry_price > 0 else 0
            logging.info(f"Actual P&L: {actual_pnl:.8f} ({actual_pnl_pct:.2f}%)")

            logging.info(f"Successfully exited position: {symbol}, amount: {filled_amount:.8f}, price: {execution_price:.8f}")

            # Calculate proceeds from the sale (filled_amount * execution_price)
            proceeds = filled_amount * execution_price

            # Create detailed result
            quote_currency = self.get_quote_currency(symbol)
            result = {
                'success': True,
                'symbol': symbol,
                'side': 'sell',
                'amount': position_size,
                'price': execution_price,  # Use the actual execution price
                'order': order,
                'filled_amount': filled_amount,
                'fee': fee_info,
                'quote_currency': quote_currency,
                'base_currency': symbol.split('/')[0],
                'entry_price': entry_price,
                'pnl': actual_pnl,
                'pnl_pct': actual_pnl_pct,
                'proceeds': proceeds,  # Add proceeds to the return value
                'timestamp': datetime.now().isoformat()
            }

            # Log the trade
            trade_logger.log_trade(
                timestamp=datetime.now().isoformat(),
                action="sell",
                symbol=symbol,
                amount=filled_amount,
                price=execution_price,
                order_id=order.get('id', 'unknown'),
                trading_mode=trading_mode,
                success=True,
                filled_amount=filled_amount,
                fee=fee_info,
                details={
                    'entry_price': entry_price,
                    'pnl': actual_pnl,
                    'pnl_pct': actual_pnl_pct
                }
            )

            # Send trade executed notification for sell
            if hasattr(self, 'notification_manager') and self.notification_manager:
                self.notification_manager.notify(
                    'trade_executed',
                    {
                        'action': 'sell',
                        'asset': symbol,
                        'price': execution_price,
                        'amount': filled_amount,
                        'filled': filled_amount,
                        'fee': fee_info.get('cost', 0.0) if fee_info else 0.0,
                        'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')
                    }
                )

            return result

        except Exception as e:
            error_msg = f"Error exiting position for {symbol}: {e}"
            logging.error(error_msg, exc_info=True)
            trade_logger.log_trade(
                timestamp=datetime.now().isoformat(),
                action="sell",
                symbol=symbol,
                amount=0.0,
                price=price if 'price' in locals() else 0.0,
                order_id="none",
                trading_mode=trading_mode,
                success=False,
                reason=error_msg
            )
            return {'success': False, 'reason': str(e), 'symbol': symbol}

    def exit_all_positions(self) -> Dict[str, Any]:
        """
        Exit all open positions.

        Returns:
            A dictionary with the execution results.
        """
        try:
            # Get all open positions
            if self.trading_config.get('mode') == 'paper':
                positions = self.paper_trading.get_positions()
            else:
                positions = self.account_manager.get_open_positions()

            if not positions:
                logging.info("No open positions to exit.")
                return {'success': True, 'reason': 'No open positions'}

            # Filter out known non-existent market symbols to avoid unnecessary API calls
            original_positions = positions
            positions = self.filter_nonexistent_positions(positions)
            filtered_out = set(original_positions.keys()) - set(positions.keys())

            results = []
            skipped_positions = []
            skipped_nonexistent = []
            failed_positions = []

            # Exit each position
            for symbol in list(positions.keys()):
                result = self.exit_position(symbol)
                results.append(result)

                # Track skipped and failed positions for better logging
                if result.get('skipped', False):
                    if result.get('nonexistent_symbol', False):
                        skipped_nonexistent.append(symbol)
                    else:
                        skipped_positions.append(symbol)
                elif not result.get('success', False):
                    failed_positions.append(symbol)

            # Log summary of position exits
            if skipped_positions:
                logging.info(f"Skipped {len(skipped_positions)} positions below minimum order size: {', '.join(skipped_positions)}")
            if skipped_nonexistent:
                logging.info(f"Skipped {len(skipped_nonexistent)} positions for non-existent market symbols: {', '.join(skipped_nonexistent)}")
            if failed_positions:
                logging.warning(f"Failed to exit {len(failed_positions)} positions: {', '.join(failed_positions)}")

            # Check if all exits were successful (including skipped ones as successful)
            all_success = all(result.get('success', False) for result in results)

            # Reset portfolio state
            self.current_asset = None
            self.current_portfolio = {}

            return {
                'success': all_success,
                'results': results,
                'skipped_count': len(skipped_positions),
                'skipped_nonexistent_count': len(skipped_nonexistent),
                'filtered_nonexistent_count': len(filtered_out),
                'failed_count': len(failed_positions)
            }

        except Exception as e:
            logging.error(f"Error exiting all positions: {e}")
            return {'success': False, 'reason': str(e)}

    def get_current_price(self, symbol: str) -> float:
        """
        Get the current market price for a symbol.

        Args:
            symbol: The trading pair symbol (e.g., 'BTC/USDT').

        Returns:
            The current market price.
        """
        # DEBUG: Enhanced price fetching logging
        logging.info(f"[DEBUG] PRICE - {symbol}: Starting get_current_price")
        logging.info(f"[DEBUG] PRICE - {symbol}: Exchange ID: {self.exchange_id}")

        try:
            # Initialize exchange if needed
            if not hasattr(self, 'exchange') or not self.exchange:
                logging.error(f"[DEBUG] PRICE - {symbol}: Initializing exchange {self.exchange_id}")

                exchange_class = getattr(ccxt, self.exchange_id, None)
                if not exchange_class:
                    raise ValueError(f"Exchange '{self.exchange_id}' not found in ccxt.")

                # Get time offset for synchronization
                time_offset = self._synchronize_time_with_server()

                self.exchange = exchange_class({
                    'enableRateLimit': True,
                })

                # Apply time offset by overriding the milliseconds method
                if time_offset != 0:
                    original_milliseconds = self.exchange.milliseconds
                    def adjusted_milliseconds():
                        return int(original_milliseconds() - time_offset)
                    self.exchange.milliseconds = adjusted_milliseconds

                logging.error(f"[DEBUG] PRICE - {symbol}: Exchange initialized successfully")
            else:
                logging.error(f"[DEBUG] PRICE - {symbol}: Using existing exchange instance")

            # DEBUG: Check if symbol exists in markets
            if hasattr(self.exchange, 'markets') and self.exchange.markets:
                if symbol in self.exchange.markets:
                    logging.error(f"[DEBUG] PRICE - {symbol}: Symbol found in exchange markets")
                else:
                    logging.error(f"[DEBUG] PRICE - {symbol}: Symbol NOT found in exchange markets")
                    logging.error(f"[DEBUG] PRICE - {symbol}: Available markets: {list(self.exchange.markets.keys())[:10]}...")
            else:
                logging.error(f"[DEBUG] PRICE - {symbol}: Exchange markets not loaded, loading now...")
                self.exchange.load_markets()
                if symbol in self.exchange.markets:
                    logging.error(f"[DEBUG] PRICE - {symbol}: Symbol found after loading markets")
                else:
                    logging.error(f"[DEBUG] PRICE - {symbol}: Symbol still NOT found after loading markets")

            # Fetch ticker
            logging.error(f"[DEBUG] PRICE - {symbol}: Attempting to fetch ticker...")
            ticker = self.exchange.fetch_ticker(symbol)

            logging.error(f"[DEBUG] PRICE - {symbol}: Ticker fetched successfully")
            logging.error(f"[DEBUG] PRICE - {symbol}: Ticker data: {ticker}")
            logging.error(f"[DEBUG] PRICE - {symbol}: Last price: {ticker['last']}")

            return ticker['last']

        except Exception as e:
            error_msg = str(e)
            logging.error(f"[DEBUG] PRICE - {symbol}: Exception occurred: {e}")
            logging.error(f"[DEBUG] PRICE - {symbol}: Exception type: {type(e)}")
            logging.error(f"[DEBUG] PRICE - {symbol}: Error message: {error_msg}")

            if "does not have market symbol" in error_msg:
                logging.warning(f"Error getting price for {symbol}: {e}")
                logging.error(f"[DEBUG] PRICE - {symbol}: Returning -1.0 (market symbol not found)")
                return -1.0  # Special value to indicate non-existent symbol
            else:
                logging.error(f"Error getting price for {symbol}: {e}")
                logging.error(f"[DEBUG] PRICE - {symbol}: Returning 0.0 (other error)")
                return 0.0

    def get_quote_currency(self, symbol: str) -> str:
        """
        Get the quote currency from a symbol.

        Args:
            symbol: The trading pair symbol (e.g., 'BTC/USDT').

        Returns:
            The quote currency (e.g., 'USDT').
        """
        parts = symbol.split('/')
        if len(parts) == 2:
            return parts[1]
        return 'USDC'  # Default

    def get_base_currency(self, symbol: str) -> str:
        """
        Get the base currency from a symbol.

        Args:
            symbol: The trading pair symbol (e.g., 'BTC/USDT').

        Returns:
            The base currency (e.g., 'BTC').
        """
        parts = symbol.split('/')
        if len(parts) > 0:
            return parts[0]
        return ''  # Default

    def get_trading_status(self) -> Dict[str, Any]:
        """
        Get the current trading status.

        Returns:
            A dictionary with the trading status.
        """
        # Get current positions
        if self.trading_config.get('mode') == 'paper':
            positions = self.paper_trading.get_positions()
            balance = self.paper_trading.get_balance()
        else:
            positions = self.account_manager.get_open_positions()
            # Detect quote currency from positions or use exchange default
            quote_currency = 'USDC'  # Default
            if positions:
                first_symbol = list(positions.keys())[0]
                if '/' in first_symbol:
                    quote_currency = first_symbol.split('/')[1]
            else:
                # Use exchange mapping if no positions
                quote_currency = EXCHANGE_QUOTE_CURRENCIES.get(self.exchange_id.lower(), 'USDC')

            balance = {
                quote_currency: self.account_manager.get_balance(quote_currency)
            }

        # Filter out non-existent positions
        positions = self.filter_nonexistent_positions(positions)

        # Get market prices for all positions
        market_prices = {}
        for symbol in positions.keys():
            price = self.get_current_price(symbol)
            if price > 0:
                market_prices[symbol] = price

        # Calculate portfolio value
        if self.trading_config.get('mode') == 'paper':
            portfolio_value = self.paper_trading.get_portfolio_value(market_prices)
        else:
            # Simple calculation for live trading - get the quote currency from balance keys
            quote_currency = list(balance.keys())[0] if balance else 'USDC'
            portfolio_value = balance.get(quote_currency, 0.0)
            for symbol, position in positions.items():
                if symbol in market_prices:
                    portfolio_value += position.get('amount', 0.0) * market_prices[symbol]

        # Calculate actual portfolio weights based on current positions
        portfolio_weights = {}
        if portfolio_value > 0:
            for symbol, position in positions.items():
                if symbol in market_prices:
                    position_value = position.get('amount', 0.0) * market_prices[symbol]
                    weight = position_value / portfolio_value
                    portfolio_weights[symbol] = weight

        return {
            'enabled': self.is_trading_enabled,
            'mode': self.trading_config.get('mode', 'paper'),
            'current_asset': self.current_asset,
            'current_portfolio': self.current_portfolio,  # Target portfolio weights
            'actual_weights': portfolio_weights,          # Actual portfolio weights
            'last_trade_time': self.last_trade_time.isoformat() if self.last_trade_time else None,
            'positions': positions,
            'balance': balance,
            'portfolio_value': portfolio_value
        }

    def enable_trading(self, enabled: bool = True):
        """
        Enable or disable trading.

        Args:
            enabled: Whether to enable trading.
        """
        self.is_trading_enabled = enabled
        logging.info(f"Trading {'enabled' if enabled else 'disabled'}")

    def set_trading_mode(self, mode: str):
        """
        Set the trading mode.

        Args:
            mode: The trading mode ('paper' or 'live').
        """
        if mode not in ['paper', 'live']:
            logging.error(f"Invalid trading mode: {mode}")
            return

        # Update config
        self.trading_config['mode'] = mode

        # Initialize paper trading if needed
        if mode == 'paper' and not self.paper_trading:
            # Determine quote currency based on exchange
            quote_currency = 'USDC'  # Default
            if self.exchange_id.lower() in ['bitvavo', 'kraken']:
                quote_currency = 'EUR'
            elif self.exchange_id.lower() == 'binance':
                quote_currency = 'USDC'

            self.paper_trading = PaperTradingExecutor(
                initial_balance=self.trading_config.get('initial_capital', 10000.0),
                quote_currency=quote_currency
            )
            logging.info(f"Paper trading initialized with {quote_currency} as quote currency")

        logging.info(f"Trading mode set to: {mode}")

    def reset_paper_trading(self):
        """Reset the paper trading account."""
        if self.paper_trading:
            self.paper_trading.reset()
            self.current_asset = None
            self.last_trade_time = None
            logging.info("Paper trading account reset")
        else:
            logging.warning("Paper trading not initialized")
