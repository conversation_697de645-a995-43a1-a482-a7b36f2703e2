{"strategy_execution": "🔄 *Strategy Execution*\n\nStatus: {status}\nBest asset: {best_asset}\n{mtpi_info}Allocation approach: {allocation_approach}\n{market_warning}{assets_traded_str}{asset_scores_str}Time: {time}", "strategy_execution_bearish": "❌❌❌ *BEARISH MARKET DETECTED* ❌❌❌\n\n🚨 **CAUTION: STAY OUT OF MEMECOIN MARKET** 🚨\n\nStatus: {status}\nBest asset: {best_asset} (⚠️ FOR REFERENCE ONLY)\n{mtpi_info}Allocation approach: {allocation_approach}\n\n🔴 **RECOMMENDATION: AVOID MEMECOIN INVESTMENTS** 🔴\n🔴 **BEARISH TREND DETECTED - HIGH RISK PERIOD** 🔴\n\n{assets_traded_str}📊 Asset rankings (informational only):\n{asset_scores_str}\n⚠️ **These rankings are for reference only during bearish conditions** ⚠️\n\nTime: {time}", "trade_executed": "💰 *Trade Executed*\n\nAction: {action}\nAsset: {asset}\nPrice: {price}\nAmount: {amount}\nFilled: {filled}\nFee: {fee}\nTime: {time}", "mtpi_signal_change": "🔔 *MTPI Signal Change*\n\nNew signal: {signal}\nPrevious signal: {previous_signal}\nTime: {time}", "mtpi_signal_change_bearish": "❌❌❌ *BEARISH SIGNAL ALERT* ❌❌❌\n\n🚨 **MTPI SIGNAL TURNED BEARISH** 🚨\n\nNew signal: ❌ {signal} ❌\nPrevious signal: {previous_signal}\n{mtpi_info}\n🔴 **RECOMMENDATION: EXIT MEMECOIN POSITIONS** 🔴\n🔴 **AVOID NEW MEMECOIN INVESTMENTS** 🔴\n🔴 **HIGH RISK MARKET CONDITIONS** 🔴\n\nTime: {time}", "asset_rotation": "🔄 *Asset Rotation*\n\nNew best asset: {new_asset}\nPrevious best asset: {previous_asset}\nTime: {time}", "asset_rotation_detailed": "🔄 *Asset Rotation Executed*\n\n📤 **SOLD:** {sold_asset}\n💰 Amount: {sold_amount}\n💵 Price: {sold_price}\n💸 Fee: {sold_fee}\n\n📥 **BOUGHT:** {bought_asset}\n💰 Amount: {bought_amount}\n💵 Price: {bought_price}\n💸 Fee: {bought_fee}\n\n📊 **Reason:** Strategy selected {bought_asset} as new best asset\n⏰ Time: {time}", "asset_rotation_simple": "🔄 *Asset Rotation*\n\n📤 Sold: {sold_asset}\n📥 Bought: {bought_asset}\n\n📊 Strategy rotated from {sold_asset} to {bought_asset}\n⏰ Time: {time}", "performance_update": "📈 *Performance Update*\n\nTotal return: {total_return}%\n7-day return: {return_7d}%\n30-day return: {return_30d}%\nMax drawdown: {max_drawdown}%\nTime: {time}", "error": "❌ Error\n\nType: {error_type}\nMessage: {error_message}\nTime: {time}", "error_alert": "⚠️ Error Alert\n\nError: {error}\nComponent: {component}\nTime: {time}", "service_status": "🔄 *Service Status*\n\nStatus: {status}\nMode: {mode}\nTrading enabled: {trading_enabled}\nTimeframe: {timeframe}\nAssets: {assets}\nAllocation approach: {allocation_approach}\n{mtpi_info}Time: {time}", "monitoring_started": "🟢 *Monitoring Started*\n\nStrategy monitoring has been started.\nTime: {time}", "monitoring_stopped": "🔴 *Monitoring Stopped*\n\nStrategy monitoring has been stopped.\nTime: {time}", "daily_summary": "📊 *Daily Summary*\n\nDate: {date}\nBest asset: {best_asset}\nMTPI signal: {mtpi_signal}\nDaily return: {daily_return}%\nTotal return: {total_return}%\nCurrent allocation: {allocation}\nTime: {time}", "daily_summary_bearish": "❌❌❌ *BEARISH MARKET - Daily Summary* ❌❌❌\n\n🚨 **CAUTION: MEMECOIN MARKET IN BEARISH TREND** 🚨\n\nDate: {date}\nTop asset (reference only): {best_asset}\nMTPI signal: ❌ {mtpi_signal} ❌\n\n🔴 **RECOMMENDATION: STAY OUT OF MEMECOIN MARKET** 🔴\n🔴 **AVOID NEW INVESTMENTS DURING BEARISH PERIOD** 🔴\n\nTime: {time}", "weekly_summary": "📅 *Weekly Summary*\n\nWeek: {week}\nBest performing asset: {best_asset}\nWeekly return: {weekly_return}%\nTotal return: {total_return}%\nSharpe ratio: {sharpe_ratio}\nMax drawdown: {max_drawdown}%\nTrades: {num_trades}\nTime: {time}", "system_status": "🖥️ *System Status*\n\nStatus: {status}\nUptime: {uptime}\nMemory usage: {memory_usage}\nCPU usage: {cpu_usage}\nDisk usage: {disk_usage}\nTime: {time}", "parameter_change": "⚙️ *Parameter Changed*\n\nParameter: {parameter}\nOld value: {old_value}\nNew value: {new_value}\nChanged by: {changed_by}\nTime: {time}", "market_alert": "⚠️ *Market Alert*\n\nType: {alert_type}\nMessage: {message}\nAffected assets: {affected_assets}\nTime: {time}", "connection_status": "🔌 *Connection Status*\n\nStatus: {status}\nExchange: {exchange}\nMessage: {message}\nTime: {time}", "network_status": "🌐 *NETWORK STATUS UPDATE*\n\n⚡ Status: {status} ⚡\n{downtime_str}🕒 In critical window: {in_critical_window}\n{missed_executions_str}Time: {time}", "missed_execution": "⚠️ *MISSED EXECUTION DETECTED*\n\n⏰ Expected time: {expected_time}\n⏱️ Current time: {current_time}\n📊 Timeframe: {timeframe}\n\nThe system will attempt to recover by executing the strategy now.", "maintenance": "🔧 *Maintenance*\n\nAction: {action}\nSuccess: {success_count}\nFailed: {failure_count}\nTotal: {total_assets}\nTime: {time}", "performance_report": "📊 *Performance Report*\n\n{message}\nTime: {time}", "status_update": "📊 *Status Update*\n\nRunning: {running}\nLast execution: {last_execution}\nMTPI signal: {mtpi_signal}\nBest asset: {best_asset}\nTime: {time}"}